// import Carousel from "react-multi-carousel";
// import "react-multi-carousel/lib/styles.css";

// const FeedbackSection = () => {
//   const responsive = {
//     desktop: {
//       breakpoint: { max: 1900, min: 1024 },
//       items: 3,
//     },
//     tablet: {
//       breakpoint: { max: 1024, min: 768 },
//       items: 2,
//     },
//     mobile: {
//       breakpoint: { max: 767, min: 280 },
//       items: 1,
//     },
//   };

//   const feedbackData = [
//     {
//       name: "<PERSON><PERSON>",
//       role: "Software Engineer",
//       content: `We were struggling to find a benefits solution that truly addressed
//             our employees' needs. [Company Name] stepped in and transformed our
//             approach. Their expertise in workplace well-being helped us boost
//             employee satisfaction by 25%!`,
//       image: "/assets/engr1.png",
//     },
//     {
//       name: "<PERSON>",
//       role: "Software Engineer",
//       content: `Innovative solutions streamlined our benefits strategy, saving us
//             time and resources. Their support team was responsive and
//             knowledgeable, answering all our questions and addressing concerns
//             promptly. The implementation process was surprisingly smooth, and
//             our employees love the new benefits platform.`,
//       image: "/assets/engr2.png",
//     },
//     {
//       name: "Oka Tomoaki",
//       role: "Software Engineer",
//       content: `Seamless integration and exceptional support from [Company Name]'s
//             team – that's what we experienced. Their technology expertise made
//             our lives easier, and their willingness to listen to our needs was
//             impressive. The solutions they provided have improved our employees'
//             user experience,`,
//       image: "/assets/engr3.png",
//     },
//     {
//       name: "Oka Tomoaki",
//       role: "Software Engineer",
//       content: `Seamless integration and exceptional support from [Company Name]'s
//             team – that's what we experienced. Their technology expertise made
//             our lives easier, and their willingness to listen to our needs was
//             impressive. The solutions they provided have improved our employees'
//             user experience,`,
//       image: "/assets/engr3.png",
//     },
//   ];

//   return (
//     <section>
//       <h3 className="font-bold md:text-4xl text-2xl text-center">
//         Feedback from Our Partners
//       </h3>
//       <section className="md:my-28 my-10">
//         <Carousel
//           responsive={responsive}
//           autoPlay={false}
//           swipeable={true}
//           infinite={true}
//           partialVisible={false}
//           dotListClass="custom-dot-list-style"
//         >
//           {feedbackData.map((item, index) => {
//             return (
//               <article className="max-w-[352px] p-2 mx-auto" key={index}>
//                 <p>{item.content}</p>
//                 <section>
//                   <div className="flex gap-3 items-center my-3">
//                     <fieldset className="w-10 rounded-[50%]">
//                       <img
//                         className="w-full rounded-[50%]"
//                         src={item.image}
//                         alt=""
//                       />
//                     </fieldset>
//                     <div>
//                       <h5 className="font-bold">{item.name}</h5>
//                       <p>{item.role}</p>
//                     </div>
//                   </div>
//                 </section>
//               </article>
//             );
//           })}
//         </Carousel>
//       </section>
//     </section>
//   );
// };

// export default FeedbackSection;
