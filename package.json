{"name": "sovereign-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"aos": "^2.3.4", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-multi-carousel": "^2.8.5", "react-router-dom": "^6.28.0", "react-slick": "^0.30.2", "recharts": "^3.0.2", "slick-carousel": "^1.8.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/aos": "^3.0.7", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-slick": "^0.23.13", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.49", "rollup": "^4.27.4", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}