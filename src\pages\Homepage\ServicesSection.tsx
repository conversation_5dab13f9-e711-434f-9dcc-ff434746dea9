import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const ServicesSection = () => {
  return (
    <section className="bg-lightBlue">
      <section
        className="max-w-7xl mx-auto text-center py-20"
        data-aos="fade-up"
        data-aos-offset="100"
        data-aos-delay="10"
        data-aos-duration="1000"
        data-aos-easing="ease-in-out"
      >
        <h3 className="font-bold md:text-4xl text-2xl mb-4">What We Do</h3>
        <p className="font-bold">
          We develop cutting-edge solutions tailored to your organization's
          unique needs.
        </p>
        <section className="flex justify-center flex-wrap py-8">
          <section
            className="w-[19rem] p-3 text-left"
            data-aos="fade-up"
            data-aos-offset="100"
            data-aos-delay="100"
            data-aos-duration="1000"
            data-aos-easing="ease"
          >
            <h4 className="font-bold mb-4">NEXT LEVEL E-COMMERCE SOLUTIONS</h4>
            <figure className="w-full">
              <img
                className="w-full"
                src="/assets/ecommerce-profession.jpg"
                alt=""
              />
            </figure>
            <p className="text-textBlack my-2">
              Our team builds fast-loading, high-converting headless eCommerce
              websites for enterprise brands, driving their growth in the
              competitive digital landscape.
            </p>
          </section>
          <section
            className="w-[19rem] p-3 text-left"
            data-aos="fade-up"
            data-aos-offset="100"
            data-aos-delay="200"
            data-aos-duration="1000"
            data-aos-easing="ease"
          >
            <h4 className="font-bold mb-4">WEBSITE DESIGN & DEVELOPMENT</h4>
            <figure className="w-full">
              <img className="w-full" src="/assets/mobile.png" alt="" />
            </figure>
            <p className="text-textBlack my-2">
              We create beautiful websites that deliver top-notch experiences,
              keeping your users engaged and satisfied.
            </p>
          </section>
          <section
            className="w-[19rem] p-3 text-left"
            data-aos="fade-up"
            data-aos-offset="100"
            data-aos-delay="400"
            data-aos-duration="1000"
            data-aos-easing="ease"
          >
            <h4 className="font-bold mb-4">CUSTOM SOFTWARE SOLUTIONS</h4>
            <figure className="w-full">
              <img className="w-full" src="/assets/engineers.jpg" alt="" />
            </figure>
            <p className="text-textBlack my-2">
              We partner with startups and global enterprises to design and
              develop custom web and mobile applications that move their
              business forward.
            </p>
          </section>
          <section
            className="w-[19rem] p-3 text-left"
            data-aos="fade-up"
            data-aos-offset="100"
            data-aos-delay="600"
            data-aos-duration="1000"
            data-aos-easing="ease"
          >
            <h4 className="font-bold mb-4">
              {"Strategic Partnerships".toUpperCase()}
            </h4>
            <figure className="w-full">
              <img className="w-full" src="/assets/hands.jpg" alt="" />
            </figure>
            <p className="text-textBlack my-2">
              Our team builds fast-loading, high-converting headless eCommerce
              websites for enterprise brands, driving their growth in the
              competitive digital landscape.
            </p>
          </section>
        </section>
      </section>
    </section>
  );
};

export default ServicesSection;
