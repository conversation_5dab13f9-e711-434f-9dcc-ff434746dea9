import { useState } from "react";
import { TfiClose } from "react-icons/tfi";
import { AiOutlineAlignRight } from "react-icons/ai";
import Navigation from "./Navigation";
import { Link } from "react-router-dom";
import Logo from "/assets/logo.png";

const Header = () => {
  const [nav, setNav] = useState(false);

  return (
    <nav className="px-4 md:px-6 py-3 bg-white w-full shadow rounded-sm lg:py-2 lg:px-12 xl:px-16">
      <div className="items-center justify-between hidden py-4 lg:flex">
        <Link to="/" className="text-2xl font-bold leading-6 text-primary">
          <div className="flex items-center gap-2">
            <img src={Logo} alt="sovereign" />
            <p>Sovereign</p>
          </div>
        </Link>

        <ul>
          <Navigation />
        </ul>
      </div>

      {/* mobile */}
      <div className="flex items-center justify-between lg:hidden ">
        <Link to="/" className=" ">
          <img src={Logo} alt="sovereign" />
        </Link>
        <ul
          onClick={() => setNav(!nav)}
          className={`absolute bg-white h-[80vh] top-[63px] z-20 items-center w-full left-0 py-10 ${
            nav ? "block" : "hidden"
          }`}
        >
          <Navigation />
        </ul>
        <div
          className="block text-3xl lg:hidden  text-primary"
          onClick={() => setNav(!nav)}
        >
          {!nav ? <AiOutlineAlignRight /> : <TfiClose />}
        </div>
      </div>
    </nav>
  );
};

export default Header;
