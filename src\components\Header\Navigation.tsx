import { NavLink, <PERSON> } from "react-router-dom";
import { FaArrowRightLong } from "react-icons/fa6";
import Button from "../Elements/Button/Button";

const Navigation = () => {
  const links = [
    { to: "/", text: "Home" },
    { to: "/about-us", text: "About" },
    { to: "/what-we-do", text: "What We Do" },
    { to: "/products", text: "Products" },
    // { to: "/our-team", text: "Our Team" },
    { to: "/contact", text: "Contact" },
  ];

  return (
    <div className="flex flex-col items-center justify-between text-sm font-medium text-primary lg:flex-row h-96 lg:h-0">
      {links.map(({ to, text }) => (
        <NavLink
          key={to}
          to={to}
          className={({ isActive }) =>
            isActive ? "text-active font-bold lg:mr-5" : "mr-0 lg:mr-5"
          }
        >
          {text}
        </NavLink>
      ))}
      <Link to="/contact" className="lg:ml-8">
        <Button className="w-44 py-2 flex items-center justify-center gap-2 text-white text-[15px] font-bold leading-8 bg-active hover:bg-orange-600 rounded-lg group">
        Get Started Now
          <span className="transition-transform duration-300 group-hover:rotate-180">
            <FaArrowRightLong />
          </span>
        </Button>
      </Link>
    </div>
  );
};

export default Navigation;
