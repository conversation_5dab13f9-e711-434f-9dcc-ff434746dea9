// import data from "./testimonies.json";
// import Carousel from "react-multi-carousel";
// import "react-multi-carousel/lib/styles.css";

// const Testimonal = () => {
//   const responsive = {
//     desktop: {
//       breakpoint: { max: 1900, min: 1024 },
//       items: 1,
//     },
//     tablet: {
//       breakpoint: { max: 1024, min: 768 },
//       items: 1,
//     },
//     mobile: {
//       breakpoint: { max: 767, min: 280 },
//       items: 1,
//     },
//   };

//   return (
//     <section
//       className="pt-20"
//       data-aos="fade-up"
//       data-aos-offset="100"
//       data-aos-delay="10"
//       data-aos-duration="1000"
//       data-aos-easing="ease-in-out"
//       data-aos-once="true"
//     >
//       <h3 className="font-semibold md:text-4xl text-2xl text-center px-3 mb-10">
//         What People says about foodbank
//       </h3>
//       <section className="bg-testimony text-center py-28">
//         <h3 className="md:text-active text-white font-bold text-[13px] py-10 tracking-[3px]">
//           TESTIMONIAL
//         </h3>
//         <Carousel
//           responsive={responsive}
//           autoPlay={false}
//           swipeable={true}
//           infinite={true}
//           partialVisible={false}
//           dotListClass="custom-dot-list-style"
//         >
//           {data.map((item, index) => {
//             return (
//               <article key={index} className="text-white">
//                 <p className="font-bold md:text-3xl text-xl md:w-3/5 mx-auto md:leading-[44px] leading-10 px-3">
//                   "{item.text}"
//                 </p>
//                 <figure className="w-[5rem] h-[5rem] mx-auto my-6 rounded-full">
//                   <img
//                     src={item.image}
//                     alt={item.name}
//                     className="w-full h-full rounded-full"
//                   />
//                 </figure>
//                 <h4 className="font-bold">{item.name}</h4>
//                 <p className="opacity-65">{item.role}</p>
//               </article>
//             );
//           })}
//         </Carousel>
//       </section>
//     </section>
//   );
// };

// export default Testimonal;
