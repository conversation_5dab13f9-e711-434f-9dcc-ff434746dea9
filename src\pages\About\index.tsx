import person from "/assets/female.png";
import AboutCard from "../../components/Cards/AboutCard";
import AchievementCard from "../../components/Cards/AchievementCard";

const About = () => {
  return (
    <main className="font-poppins overflow-x-hidden">
      <div className="bg-gradient-to-r from-[#012060] to-[#0242C6] h-[500px] md:h-[610px]">
        <div className="bg-pattern bg-cover w-full h-full flex items-center justify-center">
          <h1 className="text-white text-5xl md:text-6xl lg:text-[69px] leading-7 md:leading-8 lg:leading-[85px]">
            About Us
          </h1>
        </div>
      </div>
      <section className="border border-[#EFF0F6] rounded-3xl p-6 my-24 w-[95%] md:[90%] lg:w-[75%] mx-auto flex flex-col xl:flex-row items-center justify-between">
        <AboutCard
          tag="About Us"
          title="Everything About"
          lineBreak="Sovereign"
          subtitle="At Sovereign Technology and Innovation Limited, our guiding
            principle is the innovative application of technology to solve
            pressing real-world issues. We have embraced a powerful mission: to
            enhance the lives of dedicated individuals, significantly
            influencing their everyday experiences. Our relentless pursuit of
            employee well-being and convenience underscores our unwavering
            commitment to progress, epitomized by our pioneering product,
            Foodbank. We envision a future where essential resources are
            effortlessly accessible, and workplaces are hubs of contentment and
            productivity. In our steadfast pursuit of this vision, we aim to
            lead as trailblazers, setting new standards with groundbreaking
            solutions that not only redefine the modern workplace but also
            inspire a broader global transformation toward a more empowered,
            fulfilled workforce."
        />
        <div className="w-full xl:w-[48%]">
          <img
            src={person}
            alt="person working"
            className="rounded-3xl w-full h-[300px] md:h-[450px] xl:h-full"
          />
        </div>
      </section>
      <section className="border border-[#EFF0F6] rounded-3xl px-6 py-8 my-24 w-[95%] md:[90%] lg:w-[80%] mx-auto flex  flex-col xl:flex-row items-start justify-between">
        <AboutCard
          tag="Mission"
          title="Sovereign's Commitment"
          lineBreak="& Mission"
          subtitle="At Sovereign Technology and Innovation Limited, our mission is to transform the lives of working individuals by harnessing the power of technology to address real-world challenges. We are committed to providing innovative solutions that enhance workplace well-being and convenience. Our focus on helping salary earners access essential resources, like food, reflects our dedication to making a meaningful impact on their lives."
        />
        <AboutCard
          tag="Vision"
          title="Sovereign's Vision for"
          lineBreak="Excellence"
          subtitle="Our vision at Sovereign Technology and Innovation Limited is to be a pioneering force in the field of technology-driven employee benefits, forever changing the landscape of workplace well-being. We aspire to be recognized as the go-to provider for convenient, innovative solutions that empower individuals and employers alike. With relentless dedication to problem-solving and a commitment to excellence, we aim to positively influence the working world's future, one innovative solution at a time."
        />
      </section>
      <section className="grid grid-cols-2 lg:grid-cols-4 gap-8 place-items-center place-content-between w-[95%] md:[90%] lg:w-[80%] mx-auto pb-16">
        <AchievementCard number="5" title="Years Of Experience" />
        <AchievementCard number="50" title="Colaborations" />
        <AchievementCard number="30" title="Employees" />
        <AchievementCard number="4" title="Awards" />
      </section>
      {/* <div className=" bg-readyImg bg-no-repeat bg-cover bg-center aspect-[90%]  w-full h-[500px] md:h-[610px] mt-24 mb-4">
        <div className="bg-[#01206073] w-full h-full flex items-center justify-center">
          <h1 className="text-white font-semibold md:font-bold text-center text-4xl md:text-6xl lg:text-[69px] leading-[65px] md:leading-[75px] lg:leading-[85px]">
            Ready To Collaborate To <br /> Archive Excellence?{" "}
          </h1>
        </div>
      </div> */}
    </main>
  );
};

export default About;
