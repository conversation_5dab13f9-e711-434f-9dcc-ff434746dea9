import { Link } from "react-router-dom";

const HeroSection = () => {
  return (
    <section className="bg-heroImg bg-no-repeat bg-cover bg-center">
      <section className="w-full h-full bg-[#012060CC]">
        <article className="max-w-[72rem] mx-auto text-center lg:py-20 md:py-10 py-10 relative">
          <h1
            className="text-white px-4 font-semibold lg:text-[69px] sm:text-6xl text-4xl 
          lg:leading-[1.2] md:leading-[1.25] leading-normal"
          >
            <span className="inline relative">
              <span
                className="border-8 -left-[1rem] md:top-auto top-0 absolute border-active rounded-xl 
                lg:w-[18.1rem] sm:w-[16rem] w-[10rem] lg:h-[5.3rem] sm:h-[4.8rem] h-[3.25rem] rotate-[-3.92deg]"
              ></span>
              Partner
            </span>{" "}
            with Sovereign <span className="text-active">Tech</span> for
            cutting-edge
            <span className="text-active relative">
              <img
                className="absolute md:right-24 -bottom-3 right-10 lg:hidden"
                src="/assets/curve.svg"
                alt=""
              />
              {" "}solutions
            </span>
          </h1>
          <img
            className="absolute right-24 top-[14.4rem] lg:block hidden"
            src="/assets/curve.svg"
            alt=""
          />
          <p className="text-white md:text-lg md:mx-20 lg:my-16 my-10 opacity-65">
            At Sovereign, We design and develop web and mobile application
            globally focusing on outstanding user experience”
          </p>
          <div className="space-x-5">

         
          <Link to="/contact">
            <button
              type="button"
              className="bg-active font-bold text-white py-3 px-5 rounded-md text-sm hover:opacity-80 group"
            >
              Get in Touch <span className="ml-3 text-lg inline-block transition-transform group-hover:translate-x-1">→</span>
            </button>
          </Link>
          <Link
            to="/about-us"
           
          >
            <button
              type="button"
              className="bg-transparent font-bold text-white py-3 px-5 rounded-md border border-white text-sm hover:opacity-80 group"
            >
              Learn more <span className="ml-3 text-lg inline-block transition-transform group-hover:translate-x-1">→</span>
            </button>
          </Link>
          </div>
        </article>
      </section>
    </section>
  );
};

export default HeroSection;
