const CollaborateSection = () => {
  return (
    <section
      data-aos="zoom-in"
      data-aos-offset="100"
      data-aos-delay="10"
      data-aos-duration="2500"
      data-aos-easing="ease"
      className="flex flex-col md:flex-row items-center justify-between md:gap-20 gap-5 max-w-[62rem] mx-auto md:px-0 px-3 py-10"
    >
      <section>
        <figure>
          <img src="/assets/values.png" alt="" />
        </figure>
      </section>
      <section className="max-w-[25.2rem] text-center md:text-left">
        <h3 className="font-semibold md:text-3xl text-2xl md:leading-[3rem] mb-8">
          Expertise Without Borders: Collaborate with Us
        </h3>
        <article className="md:pr-20">
          <p className="text-textBlack">
            Collaborate with our diverse team of experts across different
            professions to solve Your work place problem
          </p>
          <h4 className="font-semibold text-xl my-3">
            Let Help You identify Challenges
          </h4>
          <p className="text-textBlack">
            Our Team is read to jump in as per Your request to analyses and
            identify problem
          </p>
          <h4 className="font-semibold text-xl my-3">And Solve Then</h4>
          <p className="text-textBlack">
            We propose and provide solution to the problem respectively.
          </p>
        </article>
      </section>
    </section>
  );
};

export default CollaborateSection;
