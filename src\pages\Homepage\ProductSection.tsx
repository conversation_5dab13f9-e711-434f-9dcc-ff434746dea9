const ProductSection = () => {
  return (
    <section
      className="max-w-[65rem] mx-auto py-20"
      data-aos="fade-up"
      data-aos-offset="100"
      data-aos-delay="10"
      data-aos-duration="1000"
      data-aos-easing="ease-in-out"
    >
      <h3 className="font-semibold md:text-4xl text-2xl text-center px-3">
        Main Product Highlight
      </h3>
      <p className="text-center md:px-12 py-8 px-3">
        Food bank is an innovation,that is solving the food insecurity ,where
        everyone can have access to food even before their pay day,which means
        no one can go to bed hungry
      </p>
      <section
        data-aos="zoom-in"
        data-aos-offset="100"
        data-aos-delay="10"
        data-aos-duration="2500"
        data-aos-easing="ease"
        className="flex flex-col lg:flex-row overflow-x-hidden justify-between gap-5 lg:gap-0 items-center lg:px-10 py-10"
      >
        <article>
          <figure className="relative">
            <img src="/assets/foodbank.png" alt="" className="w-[500px]" />
          </figure>
        </article>
        <article className="max-w-96">
          <ol>
            <li
              data-aos="slide-left"
              data-aos-offset="100"
              data-aos-delay="10"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              className="flex p-2 mb-4 before:mt-1 before:content-['1'] before:mr-3 before:bg-[#f2672233] before:text-xs before:w-fit before:h-fit before:px-3 before:font-semibold before:py-2 before:rounded-[50%]"
            >
              <div>
                <h5 className="font-medium mb-2 text-lg">Buy Now, Pay Later</h5>
                <p className="text-textBlack">Shop for groceries a</p>
              </div>
            </li>
            <li
              data-aos="slide-left"
              data-aos-offset="100"
              data-aos-delay="10"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              className="flex p-2 mb-4 before:mt-1 before:content-['2'] before:mr-3 before:bg-[#f2672233] before:text-xs before:w-fit before:h-fit before:px-3 before:font-semibold before:py-2 before:rounded-[50%]"
            >
              <div>
                <h5 className="font-medium mb-2 text-lg">
                  Grocery Selection and Delivery
                </h5>
                <p className="text-textBlack">
                  Choose from a wide range of essentials, delivered to your
                  doorstep
                </p>
              </div>
            </li>
            <li
              data-aos="slide-left"
              data-aos-offset="100"
              data-aos-delay="10"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              className="flex p-2 mb-4 before:mt-1 before:content-['3'] before:mr-3 before:bg-[#f2672233] before:text-xs before:w-fit before:h-fit before:px-3 before:font-semibold before:py-2 before:rounded-[50%]"
            >
              <div>
                <h5 className="font-medium mb-2 text-lg">
                  Outright Payment Option
                </h5>
                <p className="text-textBlack">
                  You can order for your food items, pay now and get it
                  delivered instantly
                </p>
              </div>
            </li>
          </ol>
        </article>
      </section>
      <section className="flex flex-col md:flex-row lg:justify-start md:justify-start justify-center items-center gap-3 lg:ml-10">
        <a
        href="https://apps.apple.com/us/app/foodbankapp/id6608982689"
        target="_blank"
          data-aos="fade-up"
          data-aos-offset="100"
          data-aos-delay="500"
          data-aos-duration="1000"
          data-aos-easing="ease-in-out"
          className="bg-black text-white flex items-center gap-2 max-w-44 p-1.5 rounded-md"
        >
          <img
            src="/assets/apple.png"
            alt="download on app store"
            className="w-6 h-6"
          />
          <div className="text-xs">
            Download on the <span className="text-lg block">App Store</span>
          </div>
        </a>
        <a
        href="https://play.google.com/store/apps/details?id=com.foodbank4u.app" target="_blank"
          data-aos="fade-up"
          data-aos-offset="100"
          data-aos-delay="500"
          data-aos-duration="1000"
          data-aos-easing="ease-in-out"
          className="bg-black text-white flex items-center gap-2 max-w-44 p-1.5 rounded-md"
        >
          <img
            src="/assets/googlePlay.png"
            alt="download on google play store"
            className="w-6 h-6"
          />
          <div className="text-xs">
            Get it on <span className="text-lg block">Google Play</span>
          </div>
        </a>
      </section>
      {/* <section className="flex sm:flex-row flex-col justify-between items-center my-20 px-3 gap-3">
        <article className="max-w-[32.8rem]">
          <h3 className="font-bold text-2xl mb-4">Ready to Partner with Us?</h3>
          <p className="text-textBlack">
            Discover How Our Innovative Solutions Can Enhance Your Employee
            Benefits
          </p>
        </article>
        <button
          type="button"
          className="bg-active text-white p-3 font-bold text-xs w-52 rounded-md"
        >
          Get in Touch
        </button>
      </section> */}
    </section>
  );
};

export default ProductSection;
