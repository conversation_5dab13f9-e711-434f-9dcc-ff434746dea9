import Teammate1 from "/assets/team/teammate1.png";
import Teammate2 from "/assets/team/teammate2.png";
import Teammate3 from "/assets/team/teammate3.png";
import Teammate4 from "/assets/team/teammate4.png";
import TeamCard from "../../components/Cards/TeamCard";
import { useState } from "react";

const data = [
  {
    name: "Chiroma Buhari",
    role: "Chief Executive Officer",
    image: Teammate1,
  },
  {
    name: "Chiroma Buhari",
    role: "Software Developer",
    image: Teammate2,
  },
  {
    name: "Chiroma Buhari",
    role: "Product Designer",
    image: Teammate3,
  },
  {
    name: "<PERSON><PERSON> Buhari",
    role: "Project Manger",
    image: Teammate2,
  },
  {
    name: "Chiroma Buhari",
    role: "Chief Executive Officer",
    image: Teammate3,
  },
  {
    name: "Chiroma Buhari",
    role: "Chief Executive Officer",
    image: Teammate4,
  },
];

const Index = () => {
  const [email, setEmail] = useState("");
  return (
    <main className="font-poppins">
      <section className=" bg-team bg-no-repeat bg-cover bg-center aspect-[90%] w-full h-[500px] md:h-[610px] bg-fixed">
        <article className="bg-[#01206073] w-full h-full flex items-center justify-center">
          <div>
            <h1 className="text-white font-semibold text-center text-3xl md:text-6xl lg:text-[69px] leading-[65px] md:leading-[75px] lg:leading-[85px]">
              Meet Our Team
            </h1>
            <p className="text-white text-lg lg:text-[32px] px-6 md:px-10 lg:px-0 font-medium text-center leading-6 mt-4">
              Home {">"} People
            </p>
          </div>
        </article>
      </section>

      <section
        className="px-10"
        data-aos="fade-up"
        data-aos-offset="100"
        data-aos-delay="10"
        data-aos-duration="2500"
        data-aos-easing="ease"
      >
        <article className="text-center leading-9">
          <h3 className="text-xl font-semibold md:text-3xl">
            <span className="text-active md:text-3xl">The Sovereign’s </span>
            Team
          </h3>
          <p>
            We’re a creatives that builds great products and make brands more
            successful, while learning something new, every new day, week, month
            and year
          </p>
        </article>

        <section className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 place-items-center my-5 gap-14">
          {data.map((item, index) => (
            <TeamCard
              key={index}
              image={item.image}
              name={item.name}
              role={item.role}
            />
          ))}
        </section>

        <section
          className="mt-40 px-2 md:px-10 mb-10"
          data-aos="zoom-in"
          data-aos-offset="100"
          data-aos-delay="10"
          data-aos-duration="2500"
          data-aos-easing="ease"
        >
          <article className="text-center max-w-[40rem] mx-auto">
            <h2 className="font-bold md:text-5xl text-3xl">Have An Inquiry?</h2>
            <p className="text-textBlack md:text-[18px] my-5">
              Send us a message if You don’t want to book a meeting at this
              time, and we'll get back to you within 24 hours.
            </p>
          </article>
          <form
            action=""
            method="post"
            className="flex flex-col items-center gap-4 my-10"
          >
            <input
              type="email"
              name="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              className="border w-full p-4 mb-3"
            />
            <textarea
              name="message"
              id="message"
              placeholder="Enter your message"
              className="border w-full p-4 mb-3 min-h-[120px] resize-y"
            />
            <input
              type="submit"
              value="Submit Inquiry"
              className="bg-active text-white max-w-96 text-sm p-4 rounded-md"
            />
          </form>
          {/* <article className="text-center max-w-96 mx-auto text-xs md:text-base text-textBlack">
            <p>We’ll never share your details with third parties.</p>
            <Link to={""}>
              View our{" "}
              <span className="text-primary font-semibold hover:underline">
                Privacy Policy
              </span>{" "}
              for more info.
            </Link>
          </article> */}
        </section>
      </section>
    </main>
  );
};

export default Index;
