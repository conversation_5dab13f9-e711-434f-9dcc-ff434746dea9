import { FaFacebookSquare, FaInstagram, FaLinkedin } from "react-icons/fa";
import { FaEnvelope, FaLocationDot, FaPhone, FaX } from "react-icons/fa6";
import { Link } from "react-router-dom";

const index = () => {
  return (
    <>
      <div className="bg-primary500 font-poppins grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-7 lg:gap-5 py-8 px-10">
        <div className="cursor-pointer">
          <img
            src="/assets/soverign.png"
            alt="sovereign"
            width={148}
            height={96}
          />
          <div className="flex text-sm text-white mt-5">
            At Sovereign Technology and Innovation Limited, our big idea is to
            revolutionize access to essentials for working individual
          </div>
          <div className="flex items-center gap-3 mt-4">
             <a href="https://instagram.com/sovereigntechnologylimited?igshid=MWZjMTM2ODFkZg==" target="_blank" rel="noopener noreferrer">
          <FaInstagram className="w-5 h-5 text-white" />
          </a>
          <a href="https://www.facebook.com/share/hjULsRZ118iQrY1D/?mibextid=LQQJ4d" target="_blank" rel="noopener noreferrer">
         
          <FaFacebookSquare className="w-5 h-5 text-white" />
          </a>
          <a href="https://www.linkedin.com/company/sovereign-technology-innovation-limited/" target="_blank" rel="noopener noreferrer">
          <FaLinkedin className="w-5 h-5 text-white" />
      
          </a>
          <a href="https://x.com/sovereign_tech1?s=11" target="_blank" rel="noopener noreferrer">
          <FaX className="w-5 h-5 text-white" />
          </a>
          </div>
        </div>

        <div className="cursor-pointer ml-20 flex flex-col">
          <h2 className="text-sm font-normal leading-6 text-gray-400 mb-4">
            Quick Links
          </h2>
          <Link to="/about-us" className="text-[17px] text-white leading-10">What we Do </Link>
          <Link to="/contact" className="text-[17px] text-white leading-10">Contact Us</Link>
        </div>
        <div className="cursor-pointer">
          <h2 className="text-sm font-normal leading-6 text-gray-400 mb-4">
            contacts
          </h2>
          <p className="text-[17px] text-white leading-10">
            <FaPhone className="inline-block text-[#F26722] w-4 h-4 mr-2" />
            +2349159855709
          </p>
          <p className="text-[17px] text-white leading-10">
            <FaEnvelope className="inline-block text-[#F26722] w-4 h-4 mr-2" />
            <EMAIL>
          </p>
        </div>
        <div className="cursor-pointer">
          <h2 className="text-sm font-normal leading-6 text-gray-400 mb-4">
            Address
          </h2>
          <p className="text-[17px] text-white leading-10">
            <FaLocationDot className="w-4 h-4 text-[#F26722] inline-block mr-2" />
            G & M Place, Igbo-Efon, Lekki, Lagos, Nigeria.
          </p>
          <p className="text-[17px] text-white leading-10">
            <FaLocationDot className="w-4 h-4 text-[#F26722] inline-block mr-2" />
            1703 Rockhill Rd, #6311, McKinney TX 75069, U.S.A.
          </p>
        </div>
      </div>
      <div className="bg-white py-2 px-10 flex justify-center items-center">
        <div>
          <p className="text-center text-[17px] text-black leading-10">
            © 2025 Sovereign. All rights reserved, Sovereign
          </p>
        </div>
      </div>
    </>
  );
};

export default index;
