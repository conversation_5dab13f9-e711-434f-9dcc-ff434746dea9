const WhyUsCard = ({
  title,
  subtitle,
  icon,
}: {
  title: string;
  subtitle: string;
  icon?: string;
}) => {
  return (
    <div className="p-6 py-8 text-center">
      {icon && (
        <div className="mb-6 flex justify-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <img src={icon} alt={title} className="w-8 h-8" />
          </div>
        </div>
      )}
      <h3 className="text-xl font-semibold text-gray-800 mb-4">
        {title}
      </h3>
      <p className="text-gray-600 text-sm leading-relaxed">
        {subtitle}
      </p>
    </div>
  );
};

export default WhyUsCard;
