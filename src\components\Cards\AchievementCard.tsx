import { useState, useEffect, useRef } from "react";
import CountUp from "react-countup";

const AchievementCard = ({ number, title }: { number: string; title: string }) => {
  const [startCount, setStartCount] = useState(false); 
  const cardRef = useRef<HTMLDivElement>(null); 
  const targetNumber = parseInt(number, 10); 
  
  if (isNaN(targetNumber)) return null; 

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setStartCount(true); 
        }
      },
      { threshold: 0.5 } 
    );

    if (cardRef.current) {
      observer.observe(cardRef.current); 
    }

    return () => {
      if (cardRef.current) {
        observer.unobserve(cardRef.current); 
      }
    };
  }, []);

  return (
    <div ref={cardRef} className="flex flex-col items-center">
      <h1 className="font-extrabold text-5xl lg:text-6xl leading-10 text-[#161C2D]">
        {startCount ? (
          <CountUp end={targetNumber} duration={3} />
        ) : (
          "0"
        )}
        <span className="text-3xl lg:text-4xl">+</span>
      </h1>
      <p className="text-sm font-normal leading-6 text-[#161C2D]">{title}</p>
    </div>
  );
};

export default AchievementCard;
