import { useState } from "react";

const InquirySection = () => {
  const [email, setEmail] = useState("");
  return (
    <section
      className="md:w-4/5 mx-auto mb-10"
      data-aos="zoom-in"
      data-aos-offset="100"
      data-aos-delay="10"
      data-aos-duration="2500"
      data-aos-easing="ease"
    >
      {/* <section className="flex sm:flex-row flex-col justify-between items-center my-20 px-3 gap-3">
        <article className="max-w-[32.8rem]">
          <h3 className="font-bold text-2xl mb-4">Ready to Partner with Us?</h3>
          <p className="text-textBlack">
            Discover How Our Innovative Solutions Can Enhance Your Employee
            Benefits
          </p>
        </article>
        <button
          type="button"
          className="bg-active text-white p-3 font-bold text-xs w-52 rounded-md"
        >
          Get in Touch
        </button>
      </section> */}
      <section className="px-2 md:px-0">
        <article className="text-center max-w-[40rem] mx-auto">
          <h2 className="font-bold md:text-5xl text-3xl">Have An Inquiry?</h2>
          <p className="text-textBlack md:text-[18px] my-5">
            Send us a message if You don’t want to book a meeting at this time,
            and we'll get back to you within 24 hours.
          </p>
        </article>
        <form
          action=""
          method="post"
          className="flex flex-col items-center gap-4 my-10"
        >
          <input
            type="email"
            name="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            className="border w-full p-4 mb-3"
          />
          <textarea
            name="message"
            id="message"
            placeholder="Enter your message"
            className="border w-full p-4 mb-3 min-h-[120px] resize-y"
          />
          <input
            type="submit"
            value="Submit Inquiry"
            className="bg-active text-white max-w-96 text-sm p-4 rounded-md cursor-pointer hover:bg-opacity-90"
          />
        </form>
        {/* <article className="text-center max-w-96 mx-auto text-xs md:text-base text-textBlack">
          <p>We’ll never share your details with third parties.</p>
          <Link to={""}>
            View our{" "}
            <span className="text-primary font-semibold hover:underline">
              Privacy Policy
            </span>{" "}
            for more info.
          </Link>
        </article> */}
      </section>
    </section>
  );
};

export default InquirySection;
