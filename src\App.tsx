import { Route, Routes } from "react-router-dom";
import "./App.css";
import About from "./pages/About";
import Header from "./components/Header";
import Footer from "./components/Footer";
import NotFound from "./pages/NotFound";
import Index from "./pages/Homepage/Index";
import MessageSent from "./pages/MessageSent";
import { useEffect } from "react";
import Products from "./pages/Products";
import AOS from "aos";
import "aos/dist/aos.css";
import ContactUs from "./pages/ContactUs/Index";
import Services from "./pages/Services2/Services";
import OurTeam from "./pages/OurTeam/Index";
import AgentRegistration from "./pages/AgentRegistration";
import AgentLogin from "./pages/AgentLogin";
import AgentDashboard from "./pages/AgentDashboard";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import Referrals from "./pages/AgentDashboard/Referrals";
import Settings from "./pages/AgentDashboard/Settings";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute.tsx";

function App() {
  useEffect(() => {
    AOS.init();
  });

  return (
    <AuthProvider>
      <Routes>
        {/* Public routes with header and footer */}
        <Route
          path="/"
          element={
            <>
              <Header />
              <Index />
              <Footer />
            </>
          }
        />
        <Route
          path="/about-us"
          element={
            <>
              <Header />
              <About />
              <Footer />
            </>
          }
        />
        <Route
          path="/contact"
          element={
            <>
              <Header />
              <ContactUs />
              <Footer />
            </>
          }
        />
        <Route
          path="/products"
          element={
            <>
              <Header />
              <Products />
              <Footer />
            </>
          }
        />
        <Route
          path="/what-we-do"
          element={
            <>
              <Header />
              <Services />
              <Footer />
            </>
          }
        />
        <Route
          path="/our-team"
          element={
            <>
              <Header />
              <OurTeam />
              <Footer />
            </>
          }
        />
        <Route
          path="/message"
          element={
            <>
              <Header />
              <MessageSent />
              <Footer />
            </>
          }
        />

        {/* Agent routes without header and footer */}
        <Route path="/agent-registration" element={<AgentRegistration />} />
        <Route path="/agent-login" element={<AgentLogin />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/reset-password" element={<ResetPassword />} />

        {/* Protected dashboard routes */}
        <Route
          path="/agent-dashboard"
          element={
            <ProtectedRoute>
              <AgentDashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/agent-dashboard/referrals"
          element={
            <ProtectedRoute>
              <Referrals />
            </ProtectedRoute>
          }
        />
        <Route
          path="/agent-dashboard/settings"
          element={
            <ProtectedRoute>
              <Settings />
            </ProtectedRoute>
          }
        />

        <Route
          path="*"
          element={
            <>
              <Header />
              <NotFound />
              <Footer />
            </>
          }
        />
      </Routes>
    </AuthProvider>
  );
}

export default App;
