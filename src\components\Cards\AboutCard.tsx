import { Link } from "react-router-dom";
import Button from "../Elements/Button/Button";
import { FaArrowRightLong } from "react-icons/fa6";

const AboutCard = ({
  tag,
  title,
  lineBreak,
  subtitle,
  showContactLink = true,
}: {
  tag: string;
  title: string;
  lineBreak?: string;
  subtitle: string;
  showContactLink?: boolean;
}) => {
  return (
    <div className="w-full xl:w-[48%] mb-6 lg:mb-0">
      <div className="bg-gray-100 flex items-center justify-center px-3 py-1 rounded-3xl w-fit">
        <p className="text-active text-sm lg:text-base leading-6">{tag}</p>
      </div>
      <h1 className="text-primary font-normal text-3xl lg:text-4xl leading-10 my-2 lg:my-4">
        {title}
        <br />
        {lineBreak}
      </h1>
      <p className="text-sm font-light leading-5 lg:leading-6 text-gray-500">
        {subtitle}
      </p>
      {showContactLink && (
        <Link to="/contact">
          <Button className="w-44 py-2 flex items-center mt-2 lg:mt-4 font-extrabold capitalize gap-2 text-active text-[17px] leading-8 group">
            Contact Us
            <span className="transition-transform duration-300 group-hover:rotate-180">
              <FaArrowRightLong />
            </span>
          </Button>
        </Link>
      )}
    </div>
  );
};

export default AboutCard;
