import { useNavigate } from "react-router-dom";
import Button from "../../components/Elements/Button/Button";

const index = () => {
  const navigate = useNavigate();

  return (
    <main className="font-poppins">
      <div className=" bg-laptop bg-no-repeat bg-cover bg-center aspect-[90%]  w-full h-[500px] md:h-[610px] mb-4">
        <div className="bg-[#01206073] w-full h-full flex items-center justify-center">
          <h1 className="text-white font-semibold text-center text-5xl md:text-6xl lg:text-[69px] leading-[65px] md:leading-[75px] lg:leading-[85px]">
            Page not found
          </h1>
        </div>
      </div>
      <div className="flex flex-col items-center justify-center">
        <h1 className="text-[200px] md:text-[300px] lg:text-[350px] xl:text-[500px] text-active text-center [text-shadow:_0_8px_2px_rgb(0_0_0_/_10%)]">
          404
        </h1>
        <p className="text-xl lg:text-2xl font-normal text-center">Page not Found</p>
        <Button
          onClick={() => navigate("/")}
          className="w-44 py-2 mt-6 mb-20 flex items-center justify-center gap-2 text-white text-base font-bold leading-8 bg-active hover:bg-orange-500 rounded-lg "
        >
          Return Home
        </Button>
      </div>
    </main>
  );
};

export default index;
