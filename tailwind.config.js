/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      backgroundImage: {
        heroImg: "url('/assets/heroImg.jpeg')",
        readyImg: "url('/assets/images/ready.png')",
        pattern: "url('/assets/images/pattern.png')",
        laptop: "url('/assets/images/laptop.jpeg')",
        team: "url('/assets/team/team.jpeg')",
        testimony: "url('/assets/products/testimonyBG.svg')",
      },
      colors: {
        lightBlue: "#F4F7FA",
        textBlack: "#161C2DB3",
        primary: "#012060",
        primary500: "#161C2D",
        active: "#F26722",
      },
      fontFamily: {
        poppins: ["Poppins", "sans-serif"],
      },
    },
  },
  plugins: [],
};
