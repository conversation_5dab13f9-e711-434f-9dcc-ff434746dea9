import phone from "/assets/products/productImg.svg";
import phone2 from "/assets/products/productImg2.png";
import WhyUsCard from "../../components/Cards/WhyUsCard";
import GetQuickLoans from "./GetQuickLoans";
// import Testimonal from "./Testimonal";

const index = () => {
  return (
    <main className="font-poppins overflow-hidden">
      <section className="flex flex-col md:flex-row justify-between pb-20 md:pb-0 bg-gray-50">
        <div className="w-1/2 lg:h-[35rem] md:h-[30rem] overflow-hidden hidden md:block">
          <img
            src={phone}
            alt="mockup of a female hand holding an iphone"
            className="w-full object-cover object-top overflow-hidden"
          />
        </div>
        <div className="md:w-1/2">
          <h1 className="font-bold md:text-5xl text-3xl leading-relaxed md:leading-[1.3] text-primary text-center md:text-left my-4 md:mt-20">
            Eat Well Today; Pay <br className="md:block hidden" /> Later: Flexible Food <br className="md:block hidden" /> Loan For All
          </h1>
          <div className="overflow-hidden md:hidden max-w-[300px] mx-auto flex justify-center">
            <img
              src={phone2}
              alt="mockup of a female hand holding an iphone"
              className="w-full h-full object-contain object-center"
            />
          </div>

          <section className="flex flex-col mt-14 md:flex-row items-center gap-3 ">
            <a
             href="https://apps.apple.com/us/app/foodbankapp/id6608982689"
        target="_blank"
              data-aos="fade-up"
              data-aos-offset="100"
              data-aos-delay="500"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              className="bg-black text-white flex items-center gap-2 max-w-44 p-2 rounded-md"
            >
              <img src="/assets/apple.png" alt="download on app store" />
              <p className="text-xs">
                Download on the <span className="text-lg block">App Store</span>
              </p>
            </a>
            <a
            href="https://play.google.com/store/apps/details?id=com.foodbank4u.app" target="_blank"
              data-aos="fade-up"
              data-aos-offset="100"
              data-aos-delay="500"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              className="bg-black text-white flex items-center gap-2 max-w-44 p-2 rounded-md"
            >
              <img
                src="/assets/googlePlay.png"
                alt="download on google play store"
              />
              <p className="text-xs">
                Get it on <span className="text-lg block">Google Play</span>
              </p>
            </a>
          </section>
        </div>
      </section>
      <section className="bg-gray-50 py-16">
        <section className="max-w-6xl mx-auto flex flex-col items-center justify-center px-4">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-16 text-center">
            Benefits of Foodbank
          </h1>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <WhyUsCard
              icon="/assets/food.png"
              title="Food Security"
              subtitle="Foodbank ensures uninterrupted nutrition, shielding you from financial uncertainty. Our flexible loans provide stable access to wholesome meals, unaffected by life's financial ups and downs."
            />
            <WhyUsCard
              icon="/assets/financial.png"
              title="Financial Flexibility"
              subtitle="Foodbank offers convenient grocery loans to bridge financial gaps. Repay in flexible, manageable installments tailored to your budget. Our loans eliminate emergency food expenses' financial strain, ensuring consistent access to nutritious meals."
            />
            <WhyUsCard
              icon="/assets/smile.png"
              title="Convenience"
              subtitle="Foodbank streamlines your experience with a seamless online platform. Quickly apply for grocery loans, manage repayments and shop from partner stores – all in one convenient place"
            />
            <WhyUsCard
              icon="/assets/community.png"
              title="Community Support"
              subtitle="Join Foodbank's supportive community, connecting individuals committed to food security. Share experiences, insights and resources with like-minded members. Together, break financial and nutritional barriers."
            />
          </div>
        </section>
      </section>
      <GetQuickLoans />
      {/* <Testimonal /> */}
    </main>
  );
};

export default index;
