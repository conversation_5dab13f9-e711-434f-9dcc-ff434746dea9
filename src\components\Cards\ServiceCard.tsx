const ServiceCard = ({
  name,
  image,
  tags,
}: {
  name: string;
  image: string;
  tags: string[];
}) => {
  return (
    <section className="mx-3 my-5 max-w-[35rem]">
      <figure className="w-full">
        <img src={image} alt={name} />
      </figure>
      <h6 className="font-bold my-2 ml-5">{name}</h6>
      <div className="flex gap-2 flex-wrap">
        {tags.map((tag, index) => (
          <p key={index} className="bg-gray-300 px-2 py-1 rounded-md text-xs">
            {tag}
          </p>
        ))}
      </div>
    </section>
  );
};

export default ServiceCard;
