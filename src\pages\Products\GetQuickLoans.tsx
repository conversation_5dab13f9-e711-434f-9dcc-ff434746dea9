import IphoneView from "/assets/products/iphoneView.svg";

const GetQuickLoans = () => {
  return (
    <section
      className="max-w-[65rem] mx-auto pt-20 px-5"
      data-aos="fade-up"
      data-aos-offset="100"
      data-aos-delay="10"
      data-aos-duration="1000"
      data-aos-easing="ease-in-out"
    >
      <h3 className="font-semibold md:text-4xl text-2xl text-center px-3">
        How it Works
      </h3>
      <section className="flex flex-col md:flex-row justify-between items-center gap-5 pt-28">
        <figure>
          <img src={IphoneView} alt="iPhone view of foodbank homepage" />
        </figure>
        <article className="max-w-96">
          <ol className="space-y-12">
            <li
              data-aos="slide-left"
              data-aos-offset="100"
              data-aos-delay="10"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              data-aos-once="true"
              className="flex items-start gap-4"
            >
              <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-orange-600 font-semibold text-sm">1</span>
              </div>
              <div>
                <h5 className="font-semibold mb-2 text-lg text-gray-800">
                  Sign-Up
                </h5>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Sign Up using your Email address in 30 seconds
                </p>
              </div>
            </li>
            <li
              data-aos="slide-left"
              data-aos-offset="100"
              data-aos-delay="10"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              data-aos-once="true"
              className="flex items-start gap-4"
            >
              <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-orange-600 font-semibold text-sm">2</span>
              </div>
              <div>
                <h5 className="font-semibold mb-2 text-lg text-gray-800">
                  Grocery Selection
                </h5>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Choose from a wide range of essentials, delivered to your doorstep
                </p>
              </div>
            </li>
            <li
              data-aos="slide-left"
              data-aos-offset="100"
              data-aos-delay="10"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              data-aos-once="true"
              className="flex items-start gap-4"
            >
              <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-orange-600 font-semibold text-sm">3</span>
              </div>
              <div>
                <h5 className="font-semibold mb-2 text-lg text-gray-800">
                  Check Out
                </h5>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Check Out & Choose delivery date, and get immediate delivery to doorstep
                </p>
              </div>
            </li>
          </ol>
          <section className="flex flex-col mt-14 md:flex-row items-center gap-3 ">
            <a href="https://apps.apple.com/us/app/foodbankapp/id6608982689"
        target="_blank"
              data-aos="fade-up"
              data-aos-offset="100"
              data-aos-delay="500"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              data-aos-once="true"
              className="bg-black text-white flex items-center gap-2 max-w-44 p-2 rounded-md"
            >
              <img src="/assets/apple.png" alt="download on app store" />
              <p className="text-xs">
                Download on the <span className="text-lg block">App Store</span>
              </p>
            </a>
            <a
            href="https://play.google.com/store/apps/details?id=com.foodbank4u.app" target="_blank"
              data-aos="fade-up"
              data-aos-offset="100"
              data-aos-delay="500"
              data-aos-duration="1000"
              data-aos-easing="ease-in-out"
              data-aos-once="true"
              className="bg-black text-white flex items-center gap-2 max-w-44 p-2 rounded-md"
            >
              <img
                src="/assets/googlePlay.png"
                alt="download on google play store"
              />
              <p className="text-xs">
                Get it on <span className="text-lg block">Google Play</span>
              </p>
            </a>
          </section>
        </article>
      </section>
    </section>
  );
};

export default GetQuickLoans;
