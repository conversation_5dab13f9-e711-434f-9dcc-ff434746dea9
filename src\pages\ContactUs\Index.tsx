import Button from "../../components/Elements/Button/Button";
import { Link } from "react-router-dom";
import ContactCard from "../../components/Cards/ContactCard";

const index = () => {
  return (
    <main className="font-poppins ">
      <div className=" bg-laptop bg-no-repeat bg-cover bg-center aspect-[90%]  w-full h-[500px] md:h-[610px] ">
        <div className="bg-[#01206073] w-full h-full flex items-center justify-center">
          <div>
            <h1 className="text-white font-semibold text-center text-3xl md:text-6xl lg:text-[69px] leading-[65px] md:leading-[75px] lg:leading-[85px]">
              Contact Us
            </h1>
            <p className="text-white text-lg lg:text-[32px] px-6 md:px-10 lg:px-0 font-medium text-center leading-6 mt-4">
              Get in Touch with Our Team: We're Here to Help
            </p>
          </div>
        </div>
      </div>
      <div className="bg-[#F7F7F7]">
        <div className=" w-[90%] mx-auto py-16 lg:py-24 flex flex-col  lg:flex-row items-start lg:items-center justify-between">
          <div className="flex flex-col  border-b-3px border-slate-700 py-4 w-full lg:w-[40%] ">
            <h2 className="text-4xl text-[#012060] font-semibold mb-3 md:mb-5">
              Get In Touch
            </h2>
            <p className="text-primary500 text-base leading-8 font-normal mb-3 md:mb-6">
              Send us a message if You don’t want to book a meeting at this
              time, and we'll get back to you within 24 hours.
            </p>
            <div className="flex flex-col gap-2">
              <ContactCard
                title="Address"
                description="Last Floor G & M Plaza, Km 18 Lekki – Epe Express Way, Igbo-Efon, Lekki, Lagos Eti osa, Lekki, Lagos."
                number="1"
              />
              <ContactCard
                title="Phone Number "
                description="09159855709"
                number="2"
              />
              <ContactCard
                title="Email"
                description="<EMAIL>"
                number="3"
              />
              <ContactCard
                title="Working Hour"
                description="Opening Hours : Monday - Friday 10:00 am to 5pm"
                number="4"
              />
            </div>
          </div>
          <div className="flex flex-col  border-b-3px border-slate-700 py-4 w-full lg:w-[40%] ">
            <div className="bg-white rounded-md  shadow-md px-3 py-4 flex flex-col  ">
              <div className="py-4 px-3 flex flex-col">
                <h2 className="text-4xl text-[#012060] tracking-tighter text-center md:text-left font-semibold mb-3 md:mb-5">
                  Send a Message
                </h2>
                <input
                  type="text"
                  placeholder="Joe Doe"
                  className="outline-none border-[#d2d0d0] border focus:border-primary rounded-lg px-3 py-3 mb-3 "
                />
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="outline-none border-[#d2d0d0] border focus:border-primary rounded-lg px-3 py-3 mb-3 "
                />
                <textarea
                  placeholder="I'm reaching to make an enquiry regarding getting more information "
                  className="outline-none border-[#d2d0d0] h-[157px] focus:border-primary text-sm lg:text-l border rounded-lg px-3 py-3 mb-3 "
                />
                <p className="text-sm font-light text-[#292D3280] mb-5">
                  By Submitting, you agree to the processing of your personal
                  data by Sovereign tech as described in the privacy statement
                </p>

                <Link to="/message-sent" className="flex items-end justify-end">
                  <Button className="w-[218px] h-[50px]  py-1 flex items-center justify-center mt-2  font-extrabold rounded-lg   text-[17px] leading-8 group bg-[#F26722] px-5  text-white">
                    Send Message
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <div className="w-[90%] mx-auto flex flex-col lg:flex-row items-start lg:items-center justify-between py-16 lg:py-20 ">
        <div className="flex flex-col justify-between mb-10 lg:mb-0 w-[90%] lg:w-[50%]">
          <h2 className="text-3xl lg:text-4xl leading-[35px] lg:leading-[44px] mb-2 lg:mb-4 font-bold text-[#161c2d]">
            Ready to launch your next project?
          </h2>
          <p className="text-sm lg:text-[17px] leading-7 lg:leading-8 font-normal text-[#161c2d]">
            With Our Professional team You are in save hand, get in touch
          </p>
        </div>
        <Link to="/">
          <Button className="w-[218px] h-[50px] py-2 flex items-center justify-between mt-2  font-extrabold  gap-2  text-[17px] leading-8 group bg-[#F26722] px-5  text-white">
            Book a meeting
            <span className="transition-transform duration-300 group-hover:rotate-180">
              <FaArrowRightLong />
            </span>
          </Button>
        </Link>
      </div> */}
    </main>
  );
};

export default index;
