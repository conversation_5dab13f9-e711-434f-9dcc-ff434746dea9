import { FaArrowRightLong } from "react-icons/fa6";
import { Link } from "react-router-dom";

const MessageSent = () => {
  return (
    <section className="flex justify-center font-poppins">
      <section className="text-center p-5">
        <img src="/assets/messageSent.png" alt="message sent" />
        <h2 className="font-bold text-2xl my-5">Message sent</h2>
        <p className="mb-3">We will reach out as soon as We can thank You.</p>
        <button
          type="button"
          className=" mx-auto bg-active text-white text-sm py-3 px-8 font-bold rounded-md"
        >
          <Link to={"/"} className="flex gap-10">Back To Home <FaArrowRightLong className="w-6 h-6" /></Link>
        </button>
      </section>
    </section>
  );
};

export default MessageSent;
