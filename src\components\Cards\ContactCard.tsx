const ContactCard = ({
    title,
    description,
    number
  }: {
    title: string;
    description: string;
    number: string;
  }) => {
    return (
      <div className="flex space-x-2 my-2">
        <div className="flex items-center justify-center px-4 py-2 text-primary lg:px-5 lg:py-3 h-fit rounded-[50%]  text-sm  bg-[#ffebd5]">
          {number}
        </div>
        <div className="flex flex-col text-left">
          <h3 className="text-lg font-semibold tracking-tight text-[#012060] ">
            {title}
          </h3>
          <p className="w-[80%] font-poppins text-[12px] md:text-[16px]  font-light text-[#292D32CC]">
            {description}
          </p>
        </div>
      </div>
    );
  };
  
  export default ContactCard;
  