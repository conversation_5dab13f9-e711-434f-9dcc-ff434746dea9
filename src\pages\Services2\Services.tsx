import { useState } from "react";
import person from "/assets/hands.png";
import ecommerce from "/assets/profession-designer.png";
import engineers from "/assets/engineers.png";
import female from "/assets/female.png";
import AboutCard from "../../components/Cards/AboutCard";
import InquirySection from "../Homepage/InquirySection";
import { FaChevronDown, FaChevronUp } from "react-icons/fa6";

const Services = () => {
  const [showMore, setShowMore] = useState(false);

  return (
    <section>
      <div className="bg-gradient-to-r from-[#012060] to-[#0242C6] h-[500px] md:h-[610px]">
        <div className="bg-pattern bg-cover w-full h-full flex items-center justify-center">
          <h1 className="text-white text-5xl md:text-6xl lg:text-[69px] leading-7 md:leading-8 lg:leading-[85px]">
            What We Do
          </h1>
        </div>
      </div>
      <section className="border border-[#EFF0F6] rounded-3xl p-6 my-24 w-[95%] md:[90%] lg:w-[75%] mx-auto flex flex-col xl:flex-row items-center justify-between">
        <AboutCard
          tag="Problem Identification"
          title="Custom Problem Solving
solution."
          subtitle="Partner with our experts to create a workplace that thrives. We collaborate closely with you to understand your organization's unique challenges and goals, developing customized solutions that drive innovative strategies, measurable results, enhanced employee engagement, improved productivity, and better work-life balance. Together, we'll identify key areas for improvement, develop targeted solutions, implement effective change management, monitor progress, and adjust strategies to foster a culture of continuous learning and growth.
Our team of seasoned professionals brings deep knowledge of workplace well-being, proven expertise in organizational development, and a passion for driving positive change. Discover the power of tailored solutions, crafted to address your organization's distinct needs. Let's work together to create a workplace that inspires, motivates, and drives success. Unlock your workplace potential with our expert guidance and exceptional outcomes."
          showContactLink={false}
        />
        <div className="w-full xl:w-[48%]">
          <img
            src={ecommerce}
            alt="person working"
            className="rounded-3xl w-full h-[300px] md:h-[450px] xl:h-full"
          />
        </div>
      </section>
      <section className="border border-[#EFF0F6] rounded-3xl p-6 my-24 w-[95%] md:[90%] lg:w-[75%] mx-auto flex flex-col xl:flex-row items-center justify-between">
        <div className="w-full xl:w-[48%]">
          <img
            src={female}
            alt="person working"
            className="rounded-3xl w-full h-[300px] md:h-[450px] xl:h-full"
          />
        </div>
        <AboutCard
          tag="Consultation"
          title="Employee Benefit 
Consultation"
          subtitle="Our experts will guide you in integrating innovative technology solutions that elevate employee satisfaction and streamline benefits administration. By leveraging cutting-edge tools, you'll boost engagement, enhance overall well-being, and drive business performance.
Through our expert solutions, you'll be able to identify and address benefits gaps, make data-driven decisions with AI-driven insights, and implement user-friendly platforms for effortless benefits management. Partner with us to create a future-ready workforce that thrives on care, support, and optimized benefits."
          showContactLink={false}
        />
      </section>
      
      {/* Toggle Button */}
      <div className="flex justify-center my-12">
        <button
          onClick={() => setShowMore(!showMore)}
          className="flex items-center gap-2 text-[#FF6B35] font-semibold text-lg hover:text-[#e55a2b] transition-colors duration-300 group"
        >
          {showMore ? 'Show Less' : 'See More'}
          <span className="transition-transform duration-300">
            {showMore ? <FaChevronUp /> : <FaChevronDown />}
          </span>
        </button>
      </div>
      
      {/* Additional sections - shown when showMore is true */}
      {showMore && (
        <>
          <section className="border border-[#EFF0F6] rounded-3xl p-6 my-24 w-[95%] md:[90%] lg:w-[75%] mx-auto flex flex-col xl:flex-row items-center justify-between">
            <AboutCard
              tag="Work-place Wellbeing"
              title="Workplace Well 
Solution"
              subtitle="Our innovative solutions empower you to create a thriving work environment that fosters engagement, motivation, and exceptional performance. By investing in your people, you'll reap the rewards of a happy, healthy, and high-performing workforce. This, in turn, drives business success, enhances your reputation, and attracts top talent. Our expert guidance helps you navigate the complexities of modern workplace dynamics, ensuring your organization remains competitive and forward-thinking.
By partnering with us, you'll experience a transformative impact on your workplace culture. Our cutting-edge solutions and expert knowledge will help you develop a strategic well-being plan, implement effective engagement strategies, and foster a culture of continuous learning. This leads to increased productivity, improved collaboration, and elevated leadership and talent development. As your organization flourishes, you'll notice improved employee retention and recruitment, reduced turnover and absenteeism, and enhanced brand loyalty."
              showContactLink={false}
            />
            <div className="w-full xl:w-[48%]">
              <img
                src={engineers}
                alt="person working"
                className="rounded-3xl w-full h-[300px] md:h-[450px] xl:h-full"
              />
            </div>
          </section>
          <section className="border border-[#EFF0F6] rounded-3xl p-6 my-24 w-[95%] md:[90%] lg:w-[75%] mx-auto flex flex-col xl:flex-row items-center justify-between">
            <div className="w-full xl:w-[48%]">
              <img
                src={person}
                alt="person working"
                className="rounded-3xl w-full h-[300px] md:h-[450px] xl:h-full"
              />
            </div>
            <AboutCard
              tag="Partnership"
              title="Strategic Partnership 
And Integration"
              subtitle="At our core, we believe that collective action drives transformative change. That's why we partner with like-minded organizations to amplify our impact, expand our reach, and increase our effectiveness. By joining forces, we leverage each other's strengths, expertise, and resources to create a ripple effect of positive change.
Through collaborative partnerships, we unlock new opportunities to drive meaningful outcomes. We share knowledge, best practices, and innovative solutions to tackle complex challenges and address pressing social issues. Our partnerships are built on trust, mutual respect, and a shared commitment to excellence.
By partnering with us, like-minded organizations gain access to our expertise, network, and resources. Together, we'll co-create solutions, exchange ideas, and accelerate progress toward our shared goals. Join us in harnessing the power of collaboration to amplify our impact and create a brighter future for all."
              showContactLink={false}
            />
          </section>
        </>
      )}
      <InquirySection />
    </section>
  );
};

export default Services;
