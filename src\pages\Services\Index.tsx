import ServiceCard from "../../components/Cards/ServiceCard";
import ClothStoreImg from "/assets/services/clothStore.png";
import JewelryStoreImg from "/assets/services/jewelryStore.png";
import RetailStoreImg from "/assets/services/retailStore.png";
import GucciImg from "/assets/services/gucci.png";
import FaqData from "./faqData.json";
import FaqSection from "../../components/Faq/FaqSection";

const data: { name: string; image: string; tags: string[] }[] = [
  {
    name: "Clothing Store",
    image: ClothStoreImg,
    tags: ["copywriting", "Stratégie éditoriale", "brand content"],
  },
  {
    name: "Jewelry Store",
    image: JewelryStoreImg,
    tags: ["Stratégie de marque", "copywriting", "design"],
  },
  {
    name: "Retail store",
    image: RetailStoreImg,
    tags: ["communication", "Stratégie de marque", "Stratégie éditoriale"],
  },
  {
    name: "Gucci",
    image: GucciImg,
    tags: [
      "Stratégie éditoriale",
      "Stratégie de marque",
      "design",
      "copywriting",
      "brand content",
    ],
  },
];

const Index = () => {
  return (
    <main className="font-poppins">
      <div className="bg-gradient-to-r from-[#012060] to-[#0242C6] bg-no-repeat bg-cover bg-center w-full h-[500px] md:h-[610px] mb-4">
        <section className="bg-pattern bg-cover w-full h-full flex items-center justify-center">
          <article>
            <h1 className="text-white font-semibold text-center text-3xl sm:text-4xl md:text-5xl lg:text-[69px] leading-[45px] md:leading-[75px] lg:leading-[85px]">
              WHAT WE DO
            </h1>
          </article>
        </section>
      </div>

      <section className="py-10 px-3 md:px-28">
        <h3 className="text-active text-lg my-4 md:text-4xl font-semibold">
          NEXT LEVEL E-COMMERCE SOLUTIONS
        </h3>
        <article className="py-3 text-justify text-sm md:text-xl">
          Our Next-Level E-Commerce Solutions are designed to transform your
          business into a thriving online marketplace, providing a seamless
          experience for both you and your customers. We build robust and
          visually stunning online storefronts that reflect your brand’s
          identity while ensuring functionality and ease of use. Our
          comprehensive suite of services includes intuitive product catalogs,
          user-friendly navigation, real-time inventory management, and secure
          payment gateways, all tailored to meet your specific needs. By
          optimizing your platform with features like personalized product
          recommendations and advanced search capabilities, we help you enhance
          customer satisfaction, increase retention, and drive revenue growth.
        </article>
        <article className="py-3 text-justify text-sm md:text-xl">
          At Sovereign Tech, we understand that the e-commerce landscape is
          constantly evolving, and we’re committed to keeping your business
          ahead of the curve. We integrate cutting-edge technologies like
          AI-driven analytics to provide insights into customer behavior,
          helping you make data-informed decisions. Our mobile-first designs
          ensure your platform delivers an exceptional experience across all
          devices, capturing the growing market of on-the-go shoppers.
          Additionally, our solutions are built for scalability, allowing your
          e-commerce platform to expand effortlessly as your business grows.
          Whether you're targeting local audiences or planning to enter global
          markets, our customized strategies provide the tools and expertise to
          achieve sustained success.
        </article>
      </section>

      <section className="px-3 mb-20 md:px-28">
        <div className="md:gap-3 relative flex flex-col md:flex-row pb-10 px-2">
          <h5 className="italic">Work</h5>
          <h3 className="text-2xl md:text-[56px] md:leading-[67px] mb-2 sm:w-[30rem] md:w-[50rem]">
            SOLUTIONS WE HAVE CREATED FOR E-COMMERCE
          </h3>
          <button
            type="button"
            className="absolute bottom-2 text-xs font-bold right-2 pl-2 pr-16 py-2 rounded-full border border-black w-fit"
          >
            Contact
          </button>
        </div>

        <section className="grid md:grid-cols-2 my-10 w-fit mx-auto">
          {data.map((item, index) => (
            <ServiceCard
              key={index}
              name={item.name}
              image={item.image}
              tags={item.tags}
            />
          ))}
        </section>
      </section>

      <section>
        <FaqSection data={FaqData} />
      </section>

      <section className="md:w-11/12 mx-auto md:my-40">
        <section className="flex sm:flex-row flex-col justify-between items-center my-20 px-3 gap-3">
          <article className="max-w-[32.8rem]">
            <h3 className="font-bold text-2xl sm:text-4xl md:text-5xl md:leading-[58px] mb-10">
              Ready to Develop Your Next E-Commerce Service?
            </h3>
            <p className="text-textBlack text-sm md:text-base">
              With Our Professional team You are in save hand, get in touch With
              Our Professional team You are in save hand
            </p>
          </article>
          <button
            type="button"
            className="bg-active text-white p-3 font-bold text-xs w-52 rounded-md"
          >
            Book a meeting
          </button>
        </section>
      </section>
    </main>
  );
};

export default Index;
